<div class="s-block s-block--store-map" id="store-map-{{ position }}">
    <div class="container">
        {% if component.main_title %}
            <h2 class="store-map-title text-center mb-4">{{ component.main_title }}</h2>
        {% endif %}
        {% if component.description %}
            <p class="store-map-description text-center mb-8">{{ component.description }}</p>
        {% endif %}

        {% if component.store_branches and component.store_branches|length > 0 %}
            <div class="store-map-grid">
                {% for branch in component.store_branches %}
                    <div class="branch-item mb-8">
                        {% if component.show_branch_info|default(true) %}
                            <div class="branch-info mb-4">
                                <h3 class="branch-name text-lg font-bold">{{ branch.branch_name }}</h3>
                                <p class="branch-address text-gray-600">{{ branch.branch_address }}</p>
                            </div>
                        {% endif %}

                        <div class="map-container"
                             style="height: {{ component.map_height|default(400) }}px; border: 2px solid #ddd; border-radius: 8px; overflow: hidden;"
                             data-embed-code="{{ branch.google_maps_embed }}">
                            <div class="map-placeholder" style="height: 100%; display: flex; align-items: center; justify-content: center; background: #f5f5f5; color: #666;">
                                <div class="text-center">
                                    <i class="sicon-map text-4xl mb-2"></i>
                                    <p>جاري تحميل الخريطة...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-branches-placeholder text-center py-16">
                <i class="sicon-store text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-bold mb-2">لا توجد فروع مضافة حالياً</h3>
                <p class="text-gray-600">يمكنك إضافة فروع المتجر من إعدادات الكومبوننت</p>
            </div>
        {% endif %}
    </div>
</div>

