/**
 * Store Map Component
 * Handles Google Maps embed processing and HTML entity decoding
 */
(function() {
    'use strict';

    function initStoreMap() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', processStoreMaps);
        } else {
            processStoreMaps();
        }
    }

    function processStoreMaps() {
        const storeMapElements = document.querySelectorAll('.s-block--store-map');
        
        storeMapElements.forEach(element => {
            const mapContainers = element.querySelectorAll('.map-container[data-embed-code]');
            
            mapContainers.forEach(container => {
                const embedCode = container.dataset.embedCode;
                if (embedCode && embedCode.trim()) {
                    renderMap(container, embedCode);
                }
            });
        });
    }

    function renderMap(container, embedCode) {
        try {
            // Decode HTML entities
            const decodedCode = decodeHtmlEntities(embedCode);
            
            // Clear the placeholder
            container.innerHTML = '';
            
            // Check if it's a full iframe or just URL
            if (decodedCode.includes('<iframe')) {
                // It's a full iframe code
                container.innerHTML = decodedCode;
            } else if (decodedCode.startsWith('http')) {
                // It's just a URL, create iframe
                const iframe = createIframe(decodedCode);
                container.appendChild(iframe);
            } else {
                // Invalid code, show error
                showError(container, 'كود الخريطة غير صحيح');
                return;
            }

            // Ensure iframe styling
            const iframe = container.querySelector('iframe');
            if (iframe) {
                iframe.style.width = '100%';
                iframe.style.height = '100%';
                iframe.style.border = '0';
            }

        } catch (error) {
            console.error('Error rendering map:', error);
            showError(container, 'خطأ في تحميل الخريطة');
        }
    }

    function decodeHtmlEntities(str) {
        const textarea = document.createElement('textarea');
        textarea.innerHTML = str;
        return textarea.value;
    }

    function createIframe(url) {
        const iframe = document.createElement('iframe');
        iframe.src = url;
        iframe.width = '100%';
        iframe.height = '100%';
        iframe.style.border = '0';
        iframe.setAttribute('allowfullscreen', '');
        iframe.setAttribute('loading', 'lazy');
        iframe.setAttribute('referrerpolicy', 'no-referrer-when-downgrade');
        return iframe;
    }

    function showError(container, message) {
        container.innerHTML = `
            <div style="height: 100%; display: flex; align-items: center; justify-content: center; background: #f5f5f5; color: #666;">
                <div style="text-align: center;">
                    <i class="sicon-map" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
                    <p>${message}</p>
                </div>
            </div>
        `;
    }

    // Initialize when DOM is ready
    initStoreMap();

})();
