/**
 * Store Map Component - Gaming Theme
 * Handles Google Maps embed processing, responsive design, and interactive features
 */

(function() {
    'use strict';

    class StoreMapComponent {
        constructor() {
            this.components = [];
            this.resizeTimeout = null;
            this.init();
        }

        init() {
            this.findComponents();
            this.setupEventListeners();
            this.processGoogleMapsEmbeds();
            this.initializeTabs();
            this.setupResponsiveHandling();
            this.addLoadingStates();
        }

        findComponents() {
            this.components = document.querySelectorAll('.s-block--store-map');
            console.log(`Found ${this.components.length} store map components`);
        }

        setupEventListeners() {
            // Tab navigation
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('tab-button')) {
                    this.handleTabClick(e.target);
                }
            });

            // Responsive handling
            window.addEventListener('resize', () => {
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.handleResize();
                }, 250);
            });

            // Map loading events
            document.addEventListener('load', (e) => {
                if (e.target.tagName === 'IFRAME' && e.target.closest('.map-container')) {
                    this.handleMapLoad(e.target);
                }
            }, true);
        }

        processGoogleMapsEmbeds() {
            this.components.forEach(component => {
                const mapContainers = component.querySelectorAll('.map-container');

                mapContainers.forEach(container => {
                    // Look for iframe in wrapper
                    const wrapper = container.querySelector('.map-embed-wrapper');
                    if (wrapper) {
                        const iframe = wrapper.querySelector('iframe');
                        if (iframe) {
                            this.processIframe(iframe);
                        }
                    }
                });
            });
        }

        processIframe(iframe) {
            // Extract src from iframe if it's embedded in HTML
            let src = iframe.src;

            // If src is empty, try to extract from the iframe's outerHTML
            if (!src && iframe.outerHTML) {
                const srcMatch = iframe.outerHTML.match(/src=["']([^"']+)["']/);
                if (srcMatch) {
                    src = srcMatch[1];
                    iframe.src = src;
                }
            }

            // Additional processing for Google Maps embed codes
            if (src && src.includes('google.com/maps')) {
                // Ensure the URL has proper parameters for embedding
                if (!src.includes('output=embed')) {
                    src = src.includes('?') ? src + '&output=embed' : src + '?output=embed';
                    iframe.src = src;
                }

                // Log successful map processing
                console.log('Google Maps iframe processed:', src);
            }

            // Add loading state
            const container = iframe.closest('.map-container');
            if (container) {
                container.classList.add('loading');
            }

            // Set iframe attributes for better performance and security
            iframe.setAttribute('loading', 'lazy');
            iframe.setAttribute('referrerpolicy', 'no-referrer-when-downgrade');
            iframe.setAttribute('allowfullscreen', '');
            
            // Remove any inline styles that might conflict
            iframe.style.border = 'none';
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.style.display = 'block';

            // Handle iframe load
            iframe.addEventListener('load', () => {
                if (container) {
                    container.classList.remove('loading');
                }
            });

            iframe.addEventListener('error', () => {
                if (container) {
                    container.classList.remove('loading');
                    this.handleMapError(container);
                }
            });
        }

        handleMapError(container) {
            container.innerHTML = `
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    background: var(--store-map-dark-light);
                    color: var(--store-map-text-muted);
                    text-align: center;
                    padding: 2rem;
                    border-radius: 8px;
                ">
                    <div>
                        <i class="sicon-map" style="font-size: 2rem; margin-bottom: 1rem; color: var(--store-map-primary);"></i>
                        <p>خطأ في تحميل الخريطة</p>
                        <small>يرجى التحقق من رابط الخريطة</small>
                    </div>
                </div>
            `;
        }

        initializeTabs() {
            this.components.forEach(component => {
                const tabsContainer = component.querySelector('.store-map-tabs');
                if (!tabsContainer) return;

                const tabButtons = tabsContainer.querySelectorAll('.tab-button');
                const tabPanels = tabsContainer.querySelectorAll('.tab-panel');

                // Ensure first tab is active
                if (tabButtons.length > 0 && tabPanels.length > 0) {
                    tabButtons[0].classList.add('active');
                    tabPanels[0].classList.add('active');
                }
            });
        }

        handleTabClick(button) {
            const tabsContainer = button.closest('.store-map-tabs');
            if (!tabsContainer) return;

            const targetTab = button.getAttribute('data-tab');
            const allButtons = tabsContainer.querySelectorAll('.tab-button');
            const allPanels = tabsContainer.querySelectorAll('.tab-panel');

            // Remove active class from all buttons and panels
            allButtons.forEach(btn => btn.classList.remove('active'));
            allPanels.forEach(panel => panel.classList.remove('active'));

            // Add active class to clicked button and corresponding panel
            button.classList.add('active');
            const targetPanel = tabsContainer.querySelector(`#${targetTab}`);
            if (targetPanel) {
                targetPanel.classList.add('active');
                
                // Trigger map resize for better display
                setTimeout(() => {
                    const iframe = targetPanel.querySelector('iframe');
                    if (iframe) {
                        this.triggerMapResize(iframe);
                    }
                }, 100);
            }
        }

        triggerMapResize(iframe) {
            // Trigger resize event for Google Maps
            if (iframe.contentWindow) {
                try {
                    iframe.contentWindow.dispatchEvent(new Event('resize'));
                } catch (e) {
                    // Cross-origin restrictions might prevent this
                    console.log('Could not trigger map resize due to cross-origin restrictions');
                }
            }
        }

        setupResponsiveHandling() {
            this.components.forEach(component => {
                this.updateResponsiveLayout(component);
            });
        }

        updateResponsiveLayout(component) {
            const grid = component.querySelector('.store-map-grid');
            if (!grid) return;

            const columns = parseInt(component.getAttribute('data-columns')) || 2;
            const screenWidth = window.innerWidth;

            let responsiveColumns = columns;
            
            if (screenWidth <= 768) {
                responsiveColumns = 1;
            } else if (screenWidth <= 1024) {
                responsiveColumns = Math.min(columns, 2);
            }

            grid.style.setProperty('--columns', responsiveColumns);
        }

        handleResize() {
            this.components.forEach(component => {
                this.updateResponsiveLayout(component);
                
                // Trigger map resize for visible iframes
                const visibleIframes = component.querySelectorAll('iframe');
                visibleIframes.forEach(iframe => {
                    if (this.isElementVisible(iframe)) {
                        this.triggerMapResize(iframe);
                    }
                });
            });
        }

        isElementVisible(element) {
            const rect = element.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0;
        }

        handleMapLoad(iframe) {
            const container = iframe.closest('.map-container');
            if (container) {
                container.classList.remove('loading');
                
                // Add loaded class for potential styling
                container.classList.add('loaded');
                
                // Trigger any custom events
                container.dispatchEvent(new CustomEvent('mapLoaded', {
                    detail: { iframe }
                }));
            }
        }

        addLoadingStates() {
            this.components.forEach(component => {
                const mapContainers = component.querySelectorAll('.map-container');
                mapContainers.forEach(container => {
                    const iframe = container.querySelector('iframe');
                    if (iframe && !iframe.complete) {
                        container.classList.add('loading');
                    }
                });
            });
        }

        // Public method to refresh all maps
        refresh() {
            this.findComponents();
            this.processGoogleMapsEmbeds();
            this.setupResponsiveHandling();
        }

        // Public method to extract Google Maps URL from embed code
        static extractMapUrl(embedCode) {
            if (!embedCode) return null;

            // Try to extract src from iframe tag
            const srcMatch = embedCode.match(/src=["']([^"']+)["']/);
            if (srcMatch) {
                return srcMatch[1];
            }

            // If it's already a URL, return it
            if (embedCode.startsWith('http')) {
                return embedCode;
            }

            return null;
        }

        // Public method to process and clean Google Maps embed codes
        static processEmbedCode(embedCode) {
            if (!embedCode) return null;

            // If it's already a clean iframe, return as is
            if (embedCode.trim().startsWith('<iframe') && embedCode.includes('src=')) {
                return embedCode;
            }

            // Extract URL from embed code
            const url = this.extractMapUrl(embedCode);
            if (!url) return null;

            // Create clean iframe HTML
            return `<iframe src="${url}"
                    width="100%"
                    height="100%"
                    style="border:0;"
                    allowfullscreen=""
                    loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade">
                    </iframe>`;
        }

        // Public method to validate Google Maps URL
        static isValidGoogleMapsUrl(url) {
            if (!url || typeof url !== 'string') return false;

            const googleMapsPatterns = [
                /^https:\/\/www\.google\.com\/maps\/embed/,
                /^https:\/\/maps\.google\.com/,
                /^https:\/\/www\.google\.com\/maps/
            ];

            return googleMapsPatterns.some(pattern => pattern.test(url));
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.storeMapComponent = new StoreMapComponent();
        });
    } else {
        window.storeMapComponent = new StoreMapComponent();
    }

    // Export for external use
    window.StoreMapComponent = StoreMapComponent;

})();
